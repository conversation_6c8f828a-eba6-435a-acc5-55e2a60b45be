"use client";

import { MessageS<PERSON>re, FolderOpen, Home, Plus, Settings, User, LogOut, MessageCircle, ArrowLeft, PanelLeft, PanelLeftClose } from "lucide-react";
import { useRouter } from "next/navigation";
import * as React from "react";


import { useSidebar } from "@/components/ui/sidebar";
import { Button } from "@/components/ui/button";


import {
  Sidebar,
  SidebarContent,
  SidebarHeader,
  SidebarRail,
} from "@/components/ui/sidebar";
import { ProjectChatSidebar } from "./project-chat-sidebar";
import { SidebarButton } from "./ui/sidebar-button";
import { ResizeHandle } from "./ui/resize-handle";
import { ICON_SIZES } from "@/lib/constants";
import { useAnalytics } from "@/hooks/useAnalytics";

// Mock projects data
const mockProjects = [
  { id: "1", name: "Website Redesign", status: "in-progress" },
  { id: "2", name: "Mobile App", status: "planning" },
  { id: "3", name: "Marketing Campaign", status: "completed" },
  { id: "4", name: "API Integration", status: "in-progress" },
];



interface ProjectSidebarProps extends React.ComponentProps<typeof Sidebar> {
  projectId: string;
  chatWidth?: '45%' | '45%';
  setChatWidth?: (width: '45%' | '45%') => void;
  isChatCollapsed?: boolean;
  setIsChatCollapsed?: (collapsed: boolean) => void;
  selectedBusinessItem?: any;
  resizeHandle?: {
    onMouseDown: (e: React.MouseEvent) => void;
    isDragging: boolean;
  };
}

export function ProjectSidebar({
  projectId,
  chatWidth,
  setChatWidth,
  isChatCollapsed,
  setIsChatCollapsed,
  selectedBusinessItem,
  resizeHandle,
  ...props
}: ProjectSidebarProps) {
  const { setOpen, state } = useSidebar();
  const { trackClick, trackCustomEvent } = useAnalytics();
  const router = useRouter();



  const activeProject = mockProjects.find(p => p.id === projectId) || mockProjects[0];
  const isCollapsed = state === "collapsed";






  return (
    <>

      <Sidebar
        collapsible="icon"
        className={`border-r flex flex-col h-screen relative overflow-hidden ${isCollapsed ? 'pt-2' : ''}`}
        {...props}
      >
      <SidebarHeader className={isCollapsed ? "h-20 px-4 py-4 flex items-center" : "h-20 px-4 py-4 flex items-center"}>
        {isCollapsed ? (
          /* Collapsed state - show icons vertically */
          <div className="flex flex-col items-center justify-center gap-4 w-full h-full py-11">
            {/* Sidebar Toggle */}
            <SidebarButton
              icon={PanelLeftClose}
              variant="ghost"
              size="lg"
              layout="icon-only"
              hoverColor="grey"
              hoverScale={true}
              showBorder={true}
              onClick={() => {
                trackClick("sidebar-toggle", "project-sidebar");
                trackCustomEvent("sidebar_toggled", {
                  from_state: "collapsed",
                  to_state: "expanded",
                  location: "project-sidebar"
                });
                setOpen(true);
              }}
              iconClassName={ICON_SIZES.lg}
            />

            {/* Home Icon */}
            <SidebarButton
              onClick={() => {
                trackClick("back-to-dashboard", "project-header");
                trackCustomEvent("navigation_clicked", {
                  destination: "dashboard",
                  from_page: "project-detail",
                  location: "header"
                });
                router.push('/user-dashboard');
              }}
              icon={Home}
              variant="ghost"
              size="lg"
              hoverColor="grey"
              layout="icon-only"
              borderClassName="border-gray-1 border-1"
              showBorder={true}
              hoverScale={true}
              iconClassName={ICON_SIZES.lg}
            />
          </div>
        ) : (
          /* Expanded state - show full dropdowns */
          <div className="flex items-center gap-2 h-full w-full justify-between">
            {/* Left side - Project info and sidebar toggle */}
            <div className="flex items-center gap-2">
              {/* Sidebar Toggle */}
              <SidebarButton
                icon={PanelLeft}
                variant="ghost"
                size="lg"
                layout="icon-only"
                hoverColor="grey"
                hoverScale={true}
                showBorder={true}
                onClick={() => {
                  trackClick("sidebar-toggle", "project-sidebar");
                  trackCustomEvent("sidebar_toggled", {
                    from_state: "expanded",
                    to_state: "collapsed",
                    location: "project-sidebar"
                  });
                  setOpen(false);
                }}
                iconClassName={ICON_SIZES.lg}
              />

              {/* Project Selector */}
              {!selectedBusinessItem && (
                <div className="flex items-center gap-3">
                  <SidebarButton
                    onClick={() => {
                      trackClick("back-to-dashboard", "project-header");
                      trackCustomEvent("navigation_clicked", {
                        destination: "dashboard",
                        from_page: "project-detail",
                        location: "header"
                      });
                      router.push('/user-dashboard');
                    }}
                    icon={Home}
                    variant="ghost"
                    size="lg"
                    hoverColor="grey"
                    layout="icon-only"
                    borderClassName="border-1"
                    showBorder={true}
                    hoverScale={true}
                    iconClassName={ICON_SIZES.lg}
                  />
                  <span className="text-sm font-bold text-gray-700 dark:text-gray-300 truncate">
                    {activeProject.name}
                  </span>
                </div>
              )}
            </div>

          </div>
        )}
      </SidebarHeader>

      {/* Chat Section - Fills remaining height */}
      <SidebarContent className="flex-1 min-h-0 flex flex-col w-full">
        {/* Chat Button - Only when collapsed */}
        {isCollapsed && (
          <div className="flex-shrink-0 mt-auto mb-4 flex justify-center">
            <SidebarButton
              icon={MessageCircle}
              variant="secondary"
              size="lg"
              layout="icon-only"
              showBorder={false}
              hoverColor="green"
              hoverScale={true}
              onClick={() => {
                trackClick("chat-expand-button", "project-sidebar");
                trackCustomEvent("sidebar_chat_expanded", {
                  from_state: "collapsed",
                  project_id: projectId
                });
                setOpen(true);
              }}
              iconClassName={ICON_SIZES.lg}
            />
          </div>
        )}

        {/* Chat Section - Fills remaining height when expanded */}
        {!isCollapsed && (
          <div className="flex-1 min-h-0 w-full">
            <ProjectChatSidebar
              projectId={projectId}
              embedded={true}
              chatWidth={chatWidth}
              setChatWidth={setChatWidth}
              isChatCollapsed={isChatCollapsed}
              setIsChatCollapsed={setIsChatCollapsed}
              selectedBusinessItem={selectedBusinessItem}
            />
          </div>
        )}
      </SidebarContent>

      <SidebarRail />

      {/* Resize Handle */}
      {resizeHandle && (
        <ResizeHandle
          onMouseDown={resizeHandle.onMouseDown}
          isDragging={resizeHandle.isDragging}
        />
      )}
    </Sidebar>
    </>
  );
}
